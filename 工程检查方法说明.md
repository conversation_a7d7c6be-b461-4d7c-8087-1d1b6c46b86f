# 工程检查方法优化说明

## 概述
基于原有的 `find_mapping_value` 方法，新增了工程检查功能，用于检测催收系统中的各种异常情况。

## 主要修改内容

### 1. 新增参数
- `session`: 会话对象，包含会话状态信息

### 2. 新增输出字段
- `session`: 更新后的会话状态
- `correctFlag`: 工程检查是否正确（布尔值）
- `reasoning`: 错误原因（字符串）
- `errType`: 错误类型（字符串）

### 3. 移除字段
- `intentName`: 不再需要输出意图名称

## 工程检查逻辑

### 1. 系统异常检测
**触发条件**: `intentId` 为空或空字符串
**错误类型**: "系统异常"
**说明**: 催收系统未返回有效的意图ID，可能存在系统故障

### 2. 响应超时检测
**触发条件**: 请求耗时 ≥ 3秒
**错误类型**: "响应超时"
**计算方式**: `system_time` - `extraInfo.system_time`
**说明**: 系统响应时间过长，影响用户体验

### 3. 结束语相关检测
**触发条件**: `intentId` 以 "JS-" 开头或在结束语列表中

#### 3.1 轮次过少检测
**触发条件**: 对话轮次 ≤ 4轮
**错误类型**: "轮次过少"
**说明**: 对话过早结束，可能未充分沟通

#### 3.2 通时过长检测
**触发条件**: 通话时长 ≥ 5分钟
**错误类型**: "通时过长"
**计算方式**: 从会话开始到当前的总时长
**说明**: 通话时间过长，效率较低

## 结束语意图列表
```python
ending_intents = [
    "FAQ-024", "FAQ-026", "FAQ-047", "FAQ-048", 
    "B01", "B02", "G01111", "Err", "FAQ-052"
]
```

## Session 状态管理

### 输入字段
- `start_request_time`: 会话开始时间
- `latest_request_time`: 最近一次请求时间
- `round_count`: 轮次计数

### 输出字段
- `start_request_time`: 如果session中没有，使用extraInfo的system_time
- `latest_request_time`: 使用当前extraInfo的system_time
- `latest_response_time`: 使用当前入参的system_time
- `round_count`: 原值 + 1

## 时间计算逻辑

### 时间格式
所有时间均为 "HH:MM:SS" 格式，如 "09:55:40"

### 跨天处理
当结束时间小于开始时间时，自动加上24小时进行跨天计算

### 耗时计算
- **请求耗时**: `system_time` - `extraInfo.system_time`（秒）
- **会话时长**: `system_time` - `session.start_request_time`（分钟）

## 跳过检查的场景

### 1. 特定意图ID
```python
skip_ids = [
    "BREAK", "Start", "Start2", "B01", "B02", "A01", "A02", "None", 
    "G01", "G011", "G0111", "FAQ-026", "FAQ-023", "FAQ-023-1", 
    "FAQ-023-2", "FAQ-054", "FAQ-054-1"
]
```

### 2. 语音信箱关键词
当对话内容包含以下关键词时跳过检查：
- "您继续", "语音信箱", "通讯助理", "来电助手"
- "AI助手", "语音助手", "自动服务", "提示音"
- "留言", "重录", "机主", "电话秘书", "AI秘书"
- 等其他语音信箱相关词汇

## 使用示例

### 正常调用
```python
result = find_mapping_value(
    system_time="09:55:42",
    intentId="SZ32",
    msg="客户回复内容",
    history="历史对话记录",
    extraInfo={"system_time": "09:55:40"},
    session={"round_count": 5, "start_request_time": "09:55:30"}
)

# 输出示例
{
    "extracted_history": "处理后的历史记录",
    "extracted_msg": "处理后的当轮消息",
    "true_flag": True,
    "false_flag": False,
    "session": {
        "start_request_time": "09:55:30",
        "latest_request_time": "09:55:40",
        "latest_response_time": "09:55:42",
        "round_count": 6
    },
    "correctFlag": True,
    "reasoning": "",
    "errType": ""
}
```

### 异常情况
```python
# 系统异常示例
result = find_mapping_value(
    system_time="09:55:43",
    intentId="",  # 空意图ID
    extraInfo={"system_time": "09:55:40"},
    session={"round_count": 2}
)

# 输出示例
{
    "correctFlag": False,
    "reasoning": "系统未返回有效的意图ID，可能存在系统异常",
    "errType": "系统异常",
    # ... 其他字段
}
```

## 注意事项

1. **沙盒环境兼容**: 代码避免使用 `+=`、`-=`、对象赋值等操作
2. **嵌套函数限制**: 内部函数只返回单个值
3. **时间处理**: 支持跨天时间计算
4. **异常优先级**: 系统异常 > 响应超时 > 结束语相关异常
5. **跳过逻辑**: 特定ID和语音信箱场景会跳过所有检查

## 错误类型汇总

| 错误类型 | 触发条件 | 优先级 |
|---------|---------|--------|
| 系统异常 | intentId为空 | 最高 |
| 响应超时 | 耗时≥3秒 | 高 |
| 轮次过少 | 结束语且≤4轮 | 中 |
| 通时过长 | 结束语且≥5分钟 | 中 |

这个优化版本在保持原有功能的基础上，新增了完善的工程检查能力，能够有效识别催收系统中的各种异常情况。
