def process_faq_data(data):
    """
    处理FAQ数据，合并去重排序，并提取相关信息

    入参：
    data: 包含intent_id, faq_intent_list, faq_list, script_list的字典

    出参：
    返回包含faqList, intentCategory, level, scriptList的字典
    """
    intent_id = data.get("intent_id", "")
    faq_intent_list = data.get("faq_intent_list", [])
    faq_list = data.get("faq_list", [])
    script_list = data.get("script_list", [])

    # 合并faq_intent_list和faq_list
    merged_faq_list = []

    # 添加faq_intent_list中的项目
    for item in faq_intent_list:
        merged_faq_list.append(item)

    # 添加faq_list中的项目
    for item in faq_list:
        merged_faq_list.append(item)

    # 按faqNo去重，保留score更高的
    unique_faq_list = []

    for item in merged_faq_list:
        current_faq_no = item.get("faqNo", "")
        current_score = item.get("score", 0)

        # 检查是否已存在相同faqNo的项目
        found_existing = False
        new_unique_list = []

        for existing_item in unique_faq_list:
            existing_faq_no = existing_item.get("faqNo", "")
            if existing_faq_no == current_faq_no:
                found_existing = True
                existing_score = existing_item.get("score", 0)
                if current_score > existing_score:
                    # 当前score更高，使用当前项目
                    new_unique_list.append(item)
                else:
                    # 保留原有项目
                    new_unique_list.append(existing_item)
            else:
                # 不同faqNo，保留原有项目
                new_unique_list.append(existing_item)

        if not found_existing:
            # 不存在相同faqNo，直接添加
            new_unique_list.append(item)

        # 更新列表
        unique_faq_list = new_unique_list

    # 按score从高到低排序（冒泡排序）
    n = len(unique_faq_list)
    for i in range(n):
        for j in range(0, n - i - 1):
            score1 = unique_faq_list[j].get("score", 0)
            score2 = unique_faq_list[j + 1].get("score", 0)
            if score1 < score2:
                # 交换位置
                temp = unique_faq_list[j]
                unique_faq_list[j] = unique_faq_list[j + 1]
                unique_faq_list[j + 1] = temp

    # 根据intent_id查找对应的intentCategory和level
    target_intent_category = ""
    target_level = ""

    for script in script_list:
        script_id = script.get("id", "")
        if script_id == intent_id:
            target_intent_category = script.get("intentCategory", "")
            target_level = script.get("level", "")
            break

    # 找到相同intentCategory的所有script
    same_category_scripts = []
    for script in script_list:
        script_category = script.get("intentCategory", "")
        if script_category == target_intent_category and target_intent_category != "":
            same_category_scripts.append(script)

    # 构建返回结果
    result = {
        "faqList": unique_faq_list,
        "intentCategory": target_intent_category,
        "level": target_level,
        "scriptList": same_category_scripts
    }

    return result


# 测试用例
if __name__ == "__main__":
    test_data = {
        "intent_id": "XS-001",
        "faq_intent_list": [
            {
                "faqAnswer": "FAQ-046:是这样哈，因为系统划扣在固定时间，您现在已经逾期，为避免持续拖欠影响您的征信，故建议您及时还款，那您今天务必确保还款到账，好吧？",
                "score": 1.8599143,
                "faqSimilarityQuestions": [
                    "还款期限太紧了",
                    "时间太短来不及还",
                    "还款时间安排太紧",
                    "这么快就要还款",
                    "给的还款时间太短了"
                ],
                "question": "为什么还款时间这么短？",
                "faqNo": "F-20241225174043XNRESK"
            }
        ],
        "faq_list": [
            {
                "faqAnswer": "FAQ-046:是这样哈，因为系统划扣在固定时间，您现在已经逾期，为避免持续拖欠影响您的征信，故建议您及时还款，那您今天务必确保还款到账，好吧？",
                "score": 1.8599143,
                "faqSimilarityQuestions": [
                    "还款期限太紧了",
                    "时间太短来不及还",
                    "还款时间安排太紧",
                    "这么快就要还款",
                    "给的还款时间太短了"
                ],
                "question": "为什么还款时间这么短？",
                "faqNo": "F-20241225174043XNRESK"
            }
        ],
        "script_list": [
            {
                "id": "XS-001",
                "keywords": "",
                "content": "你有困难要提前沟通，现在逾期了我也帮不了你了，现在只能先想想办法把这期账款先还掉",
                "intentCategory": "协商还款",
                "level": "3"
            },
            {
                "id": "JJ-034",
                "keywords": "",
                "content": "您要知道，逾期还款是可能会在征信上留下不良记录的，这可能会影响您未来申请贷款、信用卡，甚至租房和就业。也请你重视一下，赶紧还款维护一下信用记录好吧",
                "intentCategory": "拒绝还款",
                "level": "4"
            }
        ]
    }

    result = process_faq_data(test_data)
    print("处理结果:")
    print("faqList长度:", len(result["faqList"]))
    print("intentCategory:", result["intentCategory"])
    print("level:", result["level"])
    print("scriptList长度:", len(result["scriptList"]))
