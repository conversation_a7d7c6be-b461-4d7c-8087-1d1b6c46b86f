# 测试用例文件

def test_find_mapping_value():
    """测试工程检查方法的各种场景"""
    
    # 模拟tool装饰器
    def tool(func):
        return func
    
    # 这里复制上面的方法代码进行测试
    @tool
    def find_mapping_value(system_time, intentId="", msg="", history="", extraInfo={}, session={}):
        
        mapping_table = {
            "SZ32": "咨询是否支持微信",
            "SZ16": "要求短信"
        }

        # 跳过不检查的ID数组
        skip_ids = ["BREAK", "Start", "Start2", "B01", "B02", "A01", "A02", "None", "G01", "G011", "G0111", "FAQ-026"]
        progress_ids = ["FAQ-023", "FAQ-023", "FAQ-023-1", "FAQ-023-2", "FAQ-054", "FAQ-054-1"] 
        skip_ids = skip_ids + progress_ids
        
        # 结束语意图列表
        ending_intents = ["FAQ-024", "FAQ-026", "FAQ-047", "FAQ-048", "B01", "B02", "G01111", "Err", "FAQ-052"]
        
        # 初始化工程检查结果
        correctFlag = True
        reasoning = ""
        errType = ""
        
        # 处理session数据
        start_request_time = session.get('start_request_time', extraInfo.get('system_time', ''))
        latest_request_time = extraInfo.get('system_time', '')
        latest_response_time = system_time
        round_count = session.get('round_count', 0) + 1
        
        # 计算请求耗时（秒）
        def time_to_seconds(time_str):
            """将时间字符串转换为秒数"""
            if not time_str:
                return 0
            try:
                parts = time_str.split(':')
                if len(parts) == 3:
                    hours, minutes, seconds = map(int, parts)
                    return hours * 3600 + minutes * 60 + seconds
                return 0
            except:
                return 0
        
        request_start_seconds = time_to_seconds(extraInfo.get('system_time', ''))
        request_end_seconds = time_to_seconds(system_time)
        request_duration = request_end_seconds - request_start_seconds
        if request_duration < 0:
            request_duration = request_duration + 24 * 3600  # 跨天处理
        
        # 计算总通话时长（分钟）
        session_start_seconds = time_to_seconds(start_request_time)
        session_duration_minutes = (request_end_seconds - session_start_seconds) / 60
        if session_duration_minutes < 0:
            session_duration_minutes = session_duration_minutes + 24 * 60  # 跨天处理
        
        # 工程检查逻辑
        
        # 1. 检查系统异常（intentId为空）
        if not intentId or intentId.strip() == "":
            correctFlag = False
            reasoning = "系统未返回有效的意图ID，可能存在系统异常"
            errType = "系统异常"
        
        # 2. 检查响应超时（≥3秒）
        elif request_duration >= 3:
            correctFlag = False
            reasoning = f"请求响应耗时{request_duration}秒，超过3秒阈值"
            errType = "响应超时"
        
        # 3. 检查结束语相关异常
        elif intentId.startswith("JS-") or intentId in ending_intents:
            # 检查轮次太少（≤4轮）
            if round_count <= 4:
                correctFlag = False
                reasoning = f"对话轮次仅{round_count}轮，少于4轮最低要求"
                errType = "轮次过少"
            # 检查通话时长过长（≥5分钟）
            elif session_duration_minutes >= 5:
                correctFlag = False
                reasoning = f"通话时长{session_duration_minutes:.1f}分钟，超过5分钟阈值"
                errType = "通时过长"
        
        # 如果在跳过列表中，直接返回正确
        if intentId in skip_ids:
            return {
                "extracted_history": "",
                "extracted_msg": "",
                "true_flag": True,
                "false_flag": False,
                "session": {
                    "start_request_time": start_request_time,
                    "latest_request_time": latest_request_time,
                    "latest_response_time": latest_response_time,
                    "round_count": round_count
                },
                "correctFlag": True,
                "reasoning": "",
                "errType": ""
            }

        # 跳过语音信箱
        trimmed_input = history + msg
        voicemail_keywords = [
            "您继续", "语音信箱", "通讯助理", "来电助手", "AI助手", "语音助手", 
            "自动服务", "提示音", "留言", "重录", "机主", "电话秘书", "AI秘书", 
            "机主的秘书", "正在通话中", "暂时无法接通", "他亲自解答比较好", 
            "全部记下来了", "稍后帮您转达", "请问您还有什么要补充的吗",
            "我马上记录下来", "问到我的知识盲区了"
        ]
        
        has_voicemail = False
        for keyword in voicemail_keywords:
            if keyword in trimmed_input:
                has_voicemail = True
                break
        
        if has_voicemail:
            return {
                "extracted_history": "",
                "extracted_msg": "",
                "true_flag": True,
                "false_flag": False,
                "session": {
                    "start_request_time": start_request_time,
                    "latest_request_time": latest_request_time,
                    "latest_response_time": latest_response_time,
                    "round_count": round_count
                },
                "correctFlag": True,
                "reasoning": "",
                "errType": ""
            }
        
        # 兼容处理 \n 和 \\n
        history = history.replace('\\n', '\n')
        msg = msg.replace('\\n', '\n')

        def filter_lines(text):
            lines = text.strip().split('\n')
            kept_lines = []
            for line in lines:
                is_customer_line = "【客户】:" in line or "客户:" in line
                has_sensitive_info = "透露身份及银行信息" in line
                if not (is_customer_line and has_sensitive_info):
                    kept_lines.append(line)
            return '\n'.join(kept_lines)

        history = filter_lines(history)
        msg = filter_lines(msg)

        # 提取并分离历史记录和客户当轮回复
        history_lines = history.strip().split("\n")
        msg_lines = msg.strip().split("\n")

        # 提炼历史记录
        extracted_history = "\n".join(history_lines[:-1])  # 去掉最后一行，因为它属于当轮回复

        # 提炼当轮回复
        extracted_msg = ""
        if history_lines:
            extracted_msg = extracted_msg + history_lines[-1]
        if msg_lines:
            # 如果 history_lines 为空，或者 history_lines[-1] 不为空，则在前面加换行符
            if history_lines and history_lines[-1]:
                extracted_msg = extracted_msg + "\n"
            extracted_msg = extracted_msg + msg_lines[-1]

        return {
            "extracted_history": extracted_history,
            "extracted_msg": extracted_msg,
            "true_flag": True,
            "false_flag": False,
            "session": {
                "start_request_time": start_request_time,
                "latest_request_time": latest_request_time,
                "latest_response_time": latest_response_time,
                "round_count": round_count
            },
            "correctFlag": correctFlag,
            "reasoning": reasoning,
            "errType": errType
        }
    
    # 测试用例
    print("=== 工程检查测试用例 ===\n")
    
    # 测试1: 系统异常（intentId为空）
    print("测试1: 系统异常")
    result1 = find_mapping_value(
        system_time="09:55:43",
        intentId="",
        extraInfo={"system_time": "09:55:40"},
        session={"round_count": 2}
    )
    print(f"结果: correctFlag={result1['correctFlag']}, errType='{result1['errType']}'")
    print(f"原因: {result1['reasoning']}\n")
    
    # 测试2: 响应超时
    print("测试2: 响应超时")
    result2 = find_mapping_value(
        system_time="09:55:44",
        intentId="SZ32",
        extraInfo={"system_time": "09:55:40"},
        session={"round_count": 2}
    )
    print(f"结果: correctFlag={result2['correctFlag']}, errType='{result2['errType']}'")
    print(f"原因: {result2['reasoning']}\n")
    
    # 测试3: 轮次过少
    print("测试3: 轮次过少")
    result3 = find_mapping_value(
        system_time="09:55:42",
        intentId="JS-END",
        extraInfo={"system_time": "09:55:40"},
        session={"round_count": 3, "start_request_time": "09:55:30"}
    )
    print(f"结果: correctFlag={result3['correctFlag']}, errType='{result3['errType']}'")
    print(f"原因: {result3['reasoning']}\n")
    
    # 测试4: 通时过长
    print("测试4: 通时过长")
    result4 = find_mapping_value(
        system_time="10:01:00",
        intentId="FAQ-024",
        extraInfo={"system_time": "10:00:58"},
        session={"round_count": 10, "start_request_time": "09:55:30"}
    )
    print(f"结果: correctFlag={result4['correctFlag']}, errType='{result4['errType']}'")
    print(f"原因: {result4['reasoning']}\n")
    
    # 测试5: 正常情况
    print("测试5: 正常情况")
    result5 = find_mapping_value(
        system_time="09:55:42",
        intentId="SZ32",
        extraInfo={"system_time": "09:55:40"},
        session={"round_count": 5, "start_request_time": "09:55:30"}
    )
    print(f"结果: correctFlag={result5['correctFlag']}, errType='{result5['errType']}'")
    print(f"原因: {result5['reasoning']}\n")
    
    # 测试6: 跳过检查的ID
    print("测试6: 跳过检查的ID")
    result6 = find_mapping_value(
        system_time="09:55:45",
        intentId="BREAK",
        extraInfo={"system_time": "09:55:40"},
        session={"round_count": 1}
    )
    print(f"结果: correctFlag={result6['correctFlag']}, errType='{result6['errType']}'")
    print(f"原因: {result6['reasoning']}\n")

if __name__ == "__main__":
    test_find_mapping_value()
